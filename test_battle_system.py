#!/usr/bin/env python3
"""
测试战斗系统
"""

import sys
import os
sys.path.append('src')

# 直接导入模块避免相对导入问题
from plugins.pokemon_battle.pokemon_builder import pokemon_builder
from plugins.pokemon_battle.battle_manager import battle_manager
from plugins.pokemon_battle.data_manager import data_manager


def test_pokemon_creation():
    """测试宝可梦创建"""
    print("=== 测试宝可梦创建 ===")
    
    # 创建妙蛙种子
    bulbasaur = pokemon_builder.create_pokemon(
        species_id="bulbasaur",
        level=50,
        moves=["tackle", "vinewhip", "growl", "leechseed"]
    )
    
    if bulbasaur:
        print(f"创建成功：{bulbasaur.species.name}")
        print(f"等级：{bulbasaur.level}")
        print(f"HP：{bulbasaur.current_hp}/{bulbasaur.calculate_stat('hp')}")
        print(f"攻击：{bulbasaur.calculate_stat('atk')}")
        print(f"防御：{bulbasaur.calculate_stat('def')}")
        print(f"特攻：{bulbasaur.calculate_stat('spa')}")
        print(f"特防：{bulbasaur.calculate_stat('spd')}")
        print(f"速度：{bulbasaur.calculate_stat('spe')}")
        print(f"技能：{[move.name for move in bulbasaur.moves if move]}")
        print(f"特性：{bulbasaur.ability.name if bulbasaur.ability else '无'}")
    else:
        print("创建失败")
    
    print()


def test_team_creation():
    """测试队伍创建"""
    print("=== 测试队伍创建 ===")
    
    # 创建御三家队伍
    starter_team = pokemon_builder.create_preset_team("starter")
    
    if starter_team:
        print(f"御三家队伍创建成功，共{len(starter_team)}只宝可梦：")
        for i, pokemon in enumerate(starter_team, 1):
            print(f"{i}. {pokemon.species.name} (Lv.{pokemon.level})")
            print(f"   HP: {pokemon.calculate_stat('hp')}")
            print(f"   技能: {[move.name for move in pokemon.moves if move]}")
    else:
        print("队伍创建失败")
    
    print()


def test_data_access():
    """测试数据访问"""
    print("=== 测试数据访问 ===")
    
    # 测试宝可梦数据
    bulbasaur_species = data_manager.get_species("bulbasaur")
    if bulbasaur_species:
        print(f"宝可梦：{bulbasaur_species.name}")
        print(f"属性：{[t.value for t in bulbasaur_species.types]}")
        print(f"种族值：HP {bulbasaur_species.base_stats.hp}, 攻击 {bulbasaur_species.base_stats.atk}")
    
    # 测试技能数据
    tackle = data_manager.get_move("tackle")
    if tackle:
        print(f"技能：{tackle.name}")
        print(f"属性：{tackle.type.value}")
        print(f"威力：{tackle.power}")
        print(f"命中率：{tackle.accuracy}")
    
    print()


def test_battle_simulation():
    """测试战斗模拟"""
    print("=== 测试战斗模拟 ===")
    
    try:
        # 创建队伍
        player_team = pokemon_builder.create_preset_team("starter")
        opponent_team = pokemon_builder.create_preset_team("starter")
        
        if not player_team or not opponent_team:
            print("队伍创建失败")
            return
        
        print(f"玩家队伍：{[p.species.name for p in player_team]}")
        print(f"对手队伍：{[p.species.name for p in opponent_team]}")
        
        # 模拟战斗
        result = battle_manager.simulate_battle(player_team, opponent_team, max_turns=20)
        
        print(f"\n战斗结果：")
        print(f"获胜者：{result['winner']}")
        print(f"回合数：{result['turns']}")
        print(f"是否完成：{result['finished']}")
        
        print(f"\n战斗日志（最后10条）：")
        for message in result['log'][-10:]:
            print(f"  {message}")
            
    except Exception as e:
        print(f"战斗模拟失败：{e}")
        import traceback
        traceback.print_exc()
    
    print()


def test_quick_battle():
    """测试快速战斗"""
    print("=== 测试快速战斗 ===")
    
    try:
        # 创建快速战斗
        battle_id = battle_manager.create_quick_battle("starter", "starter")
        print(f"战斗ID：{battle_id}")
        
        # 获取战斗摘要
        summary = battle_manager.get_battle_summary(battle_id)
        print(f"玩家宝可梦：{summary['player_pokemon']['name']}")
        print(f"对手宝可梦：{summary['opponent_pokemon']['name']}")
        
        # 获取可用行动
        actions = battle_manager.get_available_actions(battle_id)
        print(f"可用技能：{[move['name'] for move in actions['moves']]}")
        
        # 执行一个行动
        messages = battle_manager.process_player_action(
            battle_id, 
            "move", 
            {"move_index": 0}
        )
        
        print(f"行动结果：")
        for message in messages:
            print(f"  {message}")
        
        # 清理
        battle_manager.end_battle(battle_id)
        
    except Exception as e:
        print(f"快速战斗测试失败：{e}")
        import traceback
        traceback.print_exc()
    
    print()


def main():
    """主测试函数"""
    print("🎮 宝可梦战斗系统测试")
    print("=" * 50)
    
    test_data_access()
    test_pokemon_creation()
    test_team_creation()
    test_battle_simulation()
    test_quick_battle()
    
    print("测试完成！")


if __name__ == "__main__":
    main()
