"""
状态效果系统 - 处理各种状态效果和异常状态
"""

from typing import Dict, List, Optional, Any, Callable
from enum import Enum
from dataclasses import dataclass
import random


class StatusType(Enum):
    """状态类型"""
    # 异常状态
    BURN = "brn"        # 烧伤
    FREEZE = "frz"      # 冰冻
    PARALYSIS = "par"   # 麻痹
    POISON = "psn"      # 中毒
    BAD_POISON = "tox"  # 剧毒
    SLEEP = "slp"       # 睡眠
    
    # 能力变化
    ATTACK_UP = "atk_up"
    ATTACK_DOWN = "atk_down"
    DEFENSE_UP = "def_up"
    DEFENSE_DOWN = "def_down"
    SPATK_UP = "spa_up"
    SPATK_DOWN = "spa_down"
    SPDEF_UP = "spd_up"
    SPDEF_DOWN = "spd_down"
    SPEED_UP = "spe_up"
    SPEED_DOWN = "spe_down"
    ACCURACY_UP = "acc_up"
    ACCURACY_DOWN = "acc_down"
    EVASION_UP = "eva_up"
    EVASION_DOWN = "eva_down"


@dataclass
class StatusEffect:
    """状态效果"""
    type: StatusType
    duration: int = -1  # -1表示永久，0表示已结束
    intensity: int = 1  # 强度（如中毒的层数）
    source: Optional[str] = None  # 来源
    data: Dict[str, Any] = None  # 额外数据
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}


class StatusManager:
    """状态管理器"""
    
    def __init__(self):
        # 状态效果处理函数
        self.status_handlers: Dict[StatusType, Callable] = {
            StatusType.BURN: self._handle_burn,
            StatusType.FREEZE: self._handle_freeze,
            StatusType.PARALYSIS: self._handle_paralysis,
            StatusType.POISON: self._handle_poison,
            StatusType.BAD_POISON: self._handle_bad_poison,
            StatusType.SLEEP: self._handle_sleep,
        }
    
    def apply_status(self, pokemon, status_type: StatusType, **kwargs) -> bool:
        """应用状态效果"""
        from .models import Pokemon
        
        # 检查是否已有相同类型的异常状态
        if self._is_major_status(status_type):
            if pokemon.status:
                return False  # 已有异常状态，无法再次施加
        
        # 检查免疫
        if self._is_immune(pokemon, status_type):
            return False
        
        # 应用状态
        if status_type in [StatusType.BURN, StatusType.FREEZE, StatusType.PARALYSIS, 
                          StatusType.POISON, StatusType.BAD_POISON, StatusType.SLEEP]:
            pokemon.status = status_type.value
            if status_type == StatusType.SLEEP:
                pokemon.volatile_status['sleep_turns'] = random.randint(1, 3)
            elif status_type == StatusType.BAD_POISON:
                pokemon.volatile_status['toxic_counter'] = 1
        else:
            # 能力变化等其他状态
            self._apply_stat_change(pokemon, status_type, kwargs.get('stages', 1))
        
        return True
    
    def remove_status(self, pokemon, status_type: StatusType):
        """移除状态效果"""
        if self._is_major_status(status_type):
            if pokemon.status == status_type.value:
                pokemon.status = None
                # 清理相关的临时数据
                if status_type == StatusType.SLEEP:
                    pokemon.volatile_status.pop('sleep_turns', None)
                elif status_type == StatusType.BAD_POISON:
                    pokemon.volatile_status.pop('toxic_counter', None)
    
    def process_end_of_turn_effects(self, pokemon) -> List[str]:
        """处理回合结束时的状态效果"""
        messages = []
        
        if pokemon.status:
            status_type = StatusType(pokemon.status)
            if status_type in self.status_handlers:
                message = self.status_handlers[status_type](pokemon)
                if message:
                    messages.append(message)
        
        return messages
    
    def can_move(self, pokemon) -> tuple[bool, Optional[str]]:
        """检查宝可梦是否能够行动"""
        if pokemon.status == StatusType.FREEZE.value:
            # 冰冻状态有20%概率解除
            if random.random() < 0.2:
                pokemon.status = None
                return True, f"{pokemon.species.name}解除了冰冻状态！"
            return False, f"{pokemon.species.name}被冰冻了，无法行动！"
        
        elif pokemon.status == StatusType.PARALYSIS.value:
            # 麻痹状态有25%概率无法行动
            if random.random() < 0.25:
                return False, f"{pokemon.species.name}因麻痹而无法行动！"
        
        elif pokemon.status == StatusType.SLEEP.value:
            sleep_turns = pokemon.volatile_status.get('sleep_turns', 0)
            if sleep_turns > 0:
                pokemon.volatile_status['sleep_turns'] = sleep_turns - 1
                if pokemon.volatile_status['sleep_turns'] == 0:
                    pokemon.status = None
                    return True, f"{pokemon.species.name}醒来了！"
                return False, f"{pokemon.species.name}正在睡觉..."
        
        return True, None
    
    def _is_major_status(self, status_type: StatusType) -> bool:
        """判断是否为主要异常状态"""
        return status_type in [
            StatusType.BURN, StatusType.FREEZE, StatusType.PARALYSIS,
            StatusType.POISON, StatusType.BAD_POISON, StatusType.SLEEP
        ]
    
    def _is_immune(self, pokemon, status_type: StatusType) -> bool:
        """检查是否免疫某种状态"""
        # 检查属性免疫
        pokemon_types = [t.value for t in pokemon.species.types]
        
        if status_type == StatusType.BURN and 'Fire' in pokemon_types:
            return True
        elif status_type == StatusType.FREEZE and 'Ice' in pokemon_types:
            return True
        elif status_type == StatusType.POISON and 'Poison' in pokemon_types:
            return True
        elif status_type == StatusType.BAD_POISON and 'Poison' in pokemon_types:
            return True
        
        # TODO: 检查特性免疫
        
        return False
    
    def _apply_stat_change(self, pokemon, status_type: StatusType, stages: int):
        """应用能力变化"""
        stat_map = {
            StatusType.ATTACK_UP: ('atk', stages),
            StatusType.ATTACK_DOWN: ('atk', -stages),
            StatusType.DEFENSE_UP: ('def', stages),
            StatusType.DEFENSE_DOWN: ('def', -stages),
            StatusType.SPATK_UP: ('spa', stages),
            StatusType.SPATK_DOWN: ('spa', -stages),
            StatusType.SPDEF_UP: ('spd', stages),
            StatusType.SPDEF_DOWN: ('spd', -stages),
            StatusType.SPEED_UP: ('spe', stages),
            StatusType.SPEED_DOWN: ('spe', -stages),
        }
        
        if status_type in stat_map:
            stat, change = stat_map[status_type]
            current = pokemon.stat_stages[stat]
            new_value = max(-6, min(6, current + change))
            pokemon.stat_stages[stat] = new_value
    
    def _handle_burn(self, pokemon) -> Optional[str]:
        """处理烧伤状态"""
        max_hp = pokemon.calculate_stat('hp')
        damage = max(1, max_hp // 16)
        pokemon.current_hp = max(0, pokemon.current_hp - damage)
        return f"{pokemon.species.name}受到了烧伤伤害！"
    
    def _handle_freeze(self, pokemon) -> Optional[str]:
        """处理冰冻状态"""
        # 冰冻状态在can_move中处理
        return None
    
    def _handle_paralysis(self, pokemon) -> Optional[str]:
        """处理麻痹状态"""
        # 麻痹状态在can_move中处理，这里不需要额外处理
        return None
    
    def _handle_poison(self, pokemon) -> Optional[str]:
        """处理中毒状态"""
        max_hp = pokemon.calculate_stat('hp')
        damage = max(1, max_hp // 8)
        pokemon.current_hp = max(0, pokemon.current_hp - damage)
        return f"{pokemon.species.name}受到了中毒伤害！"
    
    def _handle_bad_poison(self, pokemon) -> Optional[str]:
        """处理剧毒状态"""
        counter = pokemon.volatile_status.get('toxic_counter', 1)
        max_hp = pokemon.calculate_stat('hp')
        damage = max(1, max_hp * counter // 16)
        pokemon.current_hp = max(0, pokemon.current_hp - damage)
        pokemon.volatile_status['toxic_counter'] = counter + 1
        return f"{pokemon.species.name}受到了剧毒伤害！"
    
    def _handle_sleep(self, pokemon) -> Optional[str]:
        """处理睡眠状态"""
        # 睡眠状态在can_move中处理
        return None
    
    def get_status_description(self, status_type: StatusType) -> str:
        """获取状态描述"""
        descriptions = {
            StatusType.BURN: "烧伤",
            StatusType.FREEZE: "冰冻",
            StatusType.PARALYSIS: "麻痹",
            StatusType.POISON: "中毒",
            StatusType.BAD_POISON: "剧毒",
            StatusType.SLEEP: "睡眠",
        }
        return descriptions.get(status_type, "未知状态")


# 全局状态管理器实例
status_manager = StatusManager()
