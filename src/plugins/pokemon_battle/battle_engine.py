"""
战斗引擎 - 核心战斗逻辑控制器
"""

from typing import List, Dict, Optional, Any, Tu<PERSON>
from enum import Enum
from dataclasses import dataclass, field
import random

try:
    from .models import Pokemon, Move, BattleConditions
    from .damage_calculator import damage_calculator
    from .status_effects import status_manager, StatusType
    from .ability_system import ability_system, AbilityTrigger
except ImportError:
    from models import Pokemon, Move, BattleConditions
    from damage_calculator import damage_calculator
    from status_effects import status_manager, StatusType
    from ability_system import ability_system, AbilityTrigger


class BattleAction(Enum):
    """战斗行动类型"""
    MOVE = "move"
    SWITCH = "switch"
    ITEM = "item"
    RUN = "run"


@dataclass
class Action:
    """战斗行动"""
    type: BattleAction
    pokemon: Pokemon
    target: Optional[Pokemon] = None
    move: Optional[Move] = None
    switch_to: Optional[Pokemon] = None
    item: Optional[str] = None
    priority: int = 0


@dataclass
class BattleState:
    """战斗状态"""
    player_team: List[Pokemon]
    opponent_team: List[Pokemon]
    player_active: Pokemon
    opponent_active: Pokemon
    conditions: BattleConditions = field(default_factory=BattleConditions)
    turn_count: int = 0
    battle_log: List[str] = field(default_factory=list)
    is_finished: bool = False
    winner: Optional[str] = None


class BattleEngine:
    """战斗引擎"""
    
    def __init__(self):
        self.battle_state: Optional[BattleState] = None
    
    def start_battle(
        self,
        player_team: List[Pokemon],
        opponent_team: List[Pokemon]
    ) -> BattleState:
        """开始战斗"""
        if not player_team or not opponent_team:
            raise ValueError("队伍不能为空")
        
        # 初始化战斗状态
        self.battle_state = BattleState(
            player_team=player_team,
            opponent_team=opponent_team,
            player_active=player_team[0],
            opponent_active=opponent_team[0]
        )
        
        # 触发出场特性
        self._trigger_switch_in_abilities()
        
        self._log("战斗开始！")
        self._log(f"玩家派出了{self.battle_state.player_active.species.name}！")
        self._log(f"对手派出了{self.battle_state.opponent_active.species.name}！")
        
        return self.battle_state
    
    def process_turn(
        self,
        player_action: Action,
        opponent_action: Action
    ) -> List[str]:
        """处理一个回合"""
        if not self.battle_state or self.battle_state.is_finished:
            return ["战斗已结束"]
        
        self.battle_state.turn_count += 1
        turn_messages = []
        
        # 确定行动顺序
        actions = self._determine_action_order([player_action, opponent_action])
        
        # 执行行动
        for action in actions:
            if self.battle_state.is_finished:
                break
            
            messages = self._execute_action(action)
            turn_messages.extend(messages)
        
        # 回合结束处理
        if not self.battle_state.is_finished:
            end_turn_messages = self._process_end_of_turn()
            turn_messages.extend(end_turn_messages)
        
        return turn_messages
    
    def _determine_action_order(self, actions: List[Action]) -> List[Action]:
        """确定行动顺序"""
        # 按优先级和速度排序
        def action_priority(action: Action) -> Tuple[int, int]:
            priority = action.priority
            if action.type == BattleAction.MOVE and action.move:
                priority = action.move.priority
            
            # 获取宝可梦速度
            speed = action.pokemon.get_effective_stat('spe')
            
            # 麻痹状态降低速度
            if action.pokemon.status == StatusType.PARALYSIS.value:
                speed = int(speed * 0.5)
            
            return (-priority, -speed, random.random())  # 负号用于降序排列
        
        return sorted(actions, key=action_priority)
    
    def _execute_action(self, action: Action) -> List[str]:
        """执行单个行动"""
        messages = []
        
        if action.type == BattleAction.MOVE:
            messages.extend(self._execute_move(action))
        elif action.type == BattleAction.SWITCH:
            messages.extend(self._execute_switch(action))
        elif action.type == BattleAction.ITEM:
            messages.extend(self._execute_item(action))
        elif action.type == BattleAction.RUN:
            messages.extend(self._execute_run(action))
        
        return messages
    
    def _execute_move(self, action: Action) -> List[str]:
        """执行技能"""
        messages = []
        attacker = action.pokemon
        defender = action.target
        move = action.move
        
        if not move or not defender:
            return ["技能执行失败"]
        
        # 检查是否能够行动
        can_move, status_message = status_manager.can_move(attacker)
        if not can_move:
            if status_message:
                messages.append(status_message)
            return messages
        
        messages.append(f"{attacker.species.name}使用了{move.name}！")
        
        # 触发使用技能时的特性
        ability_messages = ability_system.trigger_ability(
            attacker, AbilityTrigger.ON_MOVE_USE,
            {'battle': self.battle_state, 'move': move, 'target': defender}
        )
        messages.extend(ability_messages)
        
        # 计算伤害
        damage_result = damage_calculator.calculate_damage(
            attacker, defender, move, self.battle_state.conditions
        )
        
        damage = damage_result['damage']
        
        # 触发受到伤害时的特性
        if damage > 0:
            ability_messages = ability_system.trigger_ability(
                defender, AbilityTrigger.ON_DAMAGE_TAKEN,
                {'battle': self.battle_state, 'attacker': attacker, 'move': move, 'damage': damage}
            )
            messages.extend(ability_messages)
        
        # 应用伤害
        if damage > 0:
            defender.current_hp = max(0, defender.current_hp - damage)
            
            if damage_result['is_critical']:
                messages.append("击中了要害！")
            
            effectiveness_msg = damage_calculator.get_effectiveness_message(
                damage_result['effectiveness']
            )
            if effectiveness_msg:
                messages.append(effectiveness_msg)
        
        # 处理技能的附加效果
        if move.secondary and random.random() < move.secondary.get('chance', 100) / 100:
            secondary_messages = self._apply_secondary_effects(move, attacker, defender)
            messages.extend(secondary_messages)
        
        # 处理治疗效果
        healing = damage_calculator.calculate_healing(attacker, move, damage)
        if healing > 0:
            max_hp = attacker.calculate_stat('hp')
            attacker.current_hp = min(max_hp, attacker.current_hp + healing)
            messages.append(f"{attacker.species.name}回复了HP！")
        
        # 处理反作用力伤害
        recoil = damage_calculator.calculate_recoil(attacker, move, damage)
        if recoil > 0:
            attacker.current_hp = max(0, attacker.current_hp - recoil)
            messages.append(f"{attacker.species.name}受到了反作用力伤害！")
        
        # 检查是否失去战斗能力
        if defender.is_fainted():
            messages.append(f"{defender.species.name}失去了战斗能力！")
            self._check_battle_end()
        
        if attacker.is_fainted():
            messages.append(f"{attacker.species.name}失去了战斗能力！")
            self._check_battle_end()
        
        return messages
    
    def _execute_switch(self, action: Action) -> List[str]:
        """执行换宝可梦"""
        messages = []
        
        if not action.switch_to:
            return ["换宝可梦失败"]
        
        # 确定是玩家还是对手
        if action.pokemon in self.battle_state.player_team:
            self.battle_state.player_active = action.switch_to
            messages.append(f"玩家收回了{action.pokemon.species.name}！")
            messages.append(f"玩家派出了{action.switch_to.species.name}！")
        else:
            self.battle_state.opponent_active = action.switch_to
            messages.append(f"对手收回了{action.pokemon.species.name}！")
            messages.append(f"对手派出了{action.switch_to.species.name}！")
        
        # 触发出场特性
        ability_messages = ability_system.trigger_ability(
            action.switch_to, AbilityTrigger.ON_SWITCH_IN,
            {'battle': self.battle_state}
        )
        messages.extend(ability_messages)
        
        return messages
    
    def _execute_item(self, action: Action) -> List[str]:
        """执行使用道具"""
        # TODO: 实现道具系统
        return [f"{action.pokemon.species.name}使用了道具"]
    
    def _execute_run(self, action: Action) -> List[str]:
        """执行逃跑"""
        self.battle_state.is_finished = True
        self.battle_state.winner = "opponent" if action.pokemon in self.battle_state.player_team else "player"
        return ["逃跑成功！"]
    
    def _apply_secondary_effects(self, move: Move, attacker: Pokemon, defender: Pokemon) -> List[str]:
        """应用技能的附加效果"""
        messages = []
        
        if not move.secondary:
            return messages
        
        # 状态异常
        if 'status' in move.secondary:
            status_type = StatusType(move.secondary['status'])
            if status_manager.apply_status(defender, status_type):
                status_name = status_manager.get_status_description(status_type)
                messages.append(f"{defender.species.name}陷入了{status_name}状态！")
        
        # 能力变化
        if 'boosts' in move.secondary:
            boosts = move.secondary['boosts']
            for stat, change in boosts.items():
                if stat in ['atk', 'def', 'spa', 'spd', 'spe']:
                    current = defender.stat_stages[stat]
                    new_value = max(-6, min(6, current + change))
                    defender.stat_stages[stat] = new_value
                    
                    if change > 0:
                        messages.append(f"{defender.species.name}的{stat}提升了！")
                    else:
                        messages.append(f"{defender.species.name}的{stat}下降了！")
        
        return messages
    
    def _process_end_of_turn(self) -> List[str]:
        """处理回合结束"""
        messages = []
        
        # 处理状态效果
        for pokemon in [self.battle_state.player_active, self.battle_state.opponent_active]:
            if not pokemon.is_fainted():
                status_messages = status_manager.process_end_of_turn_effects(pokemon)
                messages.extend(status_messages)
                
                # 触发回合结束特性
                ability_messages = ability_system.trigger_ability(
                    pokemon, AbilityTrigger.ON_TURN_END,
                    {'battle': self.battle_state}
                )
                messages.extend(ability_messages)
        
        # 检查战斗是否结束
        self._check_battle_end()
        
        return messages
    
    def _check_battle_end(self):
        """检查战斗是否结束"""
        player_alive = any(not p.is_fainted() for p in self.battle_state.player_team)
        opponent_alive = any(not p.is_fainted() for p in self.battle_state.opponent_team)
        
        if not player_alive:
            self.battle_state.is_finished = True
            self.battle_state.winner = "opponent"
        elif not opponent_alive:
            self.battle_state.is_finished = True
            self.battle_state.winner = "player"
    
    def _trigger_switch_in_abilities(self):
        """触发出场特性"""
        for pokemon in [self.battle_state.player_active, self.battle_state.opponent_active]:
            ability_messages = ability_system.trigger_ability(
                pokemon, AbilityTrigger.ON_SWITCH_IN,
                {'battle': self.battle_state}
            )
            self.battle_state.battle_log.extend(ability_messages)
    
    def _log(self, message: str):
        """记录战斗日志"""
        if self.battle_state:
            self.battle_state.battle_log.append(message)
    
    def get_available_moves(self, pokemon: Pokemon) -> List[Move]:
        """获取可用技能"""
        return [move for move in pokemon.moves if move]
    
    def get_available_switches(self, is_player: bool) -> List[Pokemon]:
        """获取可换的宝可梦"""
        team = self.battle_state.player_team if is_player else self.battle_state.opponent_team
        active = self.battle_state.player_active if is_player else self.battle_state.opponent_active
        
        return [p for p in team if p != active and not p.is_fainted()]


class BattleAI:
    """战斗AI"""

    def __init__(self, difficulty: str = "normal"):
        self.difficulty = difficulty

    def choose_action(self, battle_state: BattleState) -> Action:
        """AI选择行动"""
        ai_pokemon = battle_state.opponent_active
        player_pokemon = battle_state.player_active

        # 如果AI宝可梦失去战斗能力，选择换宝可梦
        if ai_pokemon.is_fainted():
            available_switches = battle_engine.get_available_switches(False)
            if available_switches:
                switch_to = self._choose_best_switch(available_switches, player_pokemon)
                return Action(
                    type=BattleAction.SWITCH,
                    pokemon=ai_pokemon,
                    switch_to=switch_to
                )

        # 选择最佳技能
        available_moves = battle_engine.get_available_moves(ai_pokemon)
        if available_moves:
            best_move = self._choose_best_move(ai_pokemon, player_pokemon, available_moves)
            return Action(
                type=BattleAction.MOVE,
                pokemon=ai_pokemon,
                target=player_pokemon,
                move=best_move
            )

        # 默认行动（理论上不应该到达这里）
        return Action(
            type=BattleAction.MOVE,
            pokemon=ai_pokemon,
            target=player_pokemon
        )

    def _choose_best_move(self, attacker: Pokemon, defender: Pokemon, moves: List[Move]) -> Move:
        """选择最佳技能"""
        best_move = moves[0]
        best_score = -1

        for move in moves:
            score = self._evaluate_move(attacker, defender, move)
            if score > best_score:
                best_score = score
                best_move = move

        return best_move

    def _evaluate_move(self, attacker: Pokemon, defender: Pokemon, move: Move) -> float:
        """评估技能效果"""
        score = 0.0

        # 基础威力评分
        score += move.power * 0.1

        # 属性相克评分
        from .data_manager import data_manager
        defender_types = [t.value for t in defender.species.types]
        effectiveness = data_manager.get_type_effectiveness(move.type.value, defender_types)
        score += effectiveness * 50

        # STAB加成
        if move.type in attacker.species.types:
            score += 25

        # 命中率评分
        if isinstance(move.accuracy, int):
            score += move.accuracy * 0.1
        elif move.accuracy is True:
            score += 10

        # 优先级评分
        score += move.priority * 10

        return score

    def _choose_best_switch(self, available_pokemon: List[Pokemon], opponent: Pokemon) -> Pokemon:
        """选择最佳换宝可梦"""
        best_pokemon = available_pokemon[0]
        best_score = -1

        for pokemon in available_pokemon:
            score = self._evaluate_matchup(pokemon, opponent)
            if score > best_score:
                best_score = score
                best_pokemon = pokemon

        return best_pokemon

    def _evaluate_matchup(self, pokemon: Pokemon, opponent: Pokemon) -> float:
        """评估对战匹配度"""
        score = 0.0

        # HP百分比评分
        hp_ratio = pokemon.current_hp / pokemon.calculate_stat('hp')
        score += hp_ratio * 50

        # 属性相克评分
        from .data_manager import data_manager
        for move in pokemon.moves:
            if move:
                opponent_types = [t.value for t in opponent.species.types]
                effectiveness = data_manager.get_type_effectiveness(move.type.value, opponent_types)
                score += effectiveness * 10

        return score


# 全局战斗引擎和AI实例
battle_engine = BattleEngine()
battle_ai = BattleAI()
