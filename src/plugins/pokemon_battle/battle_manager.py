"""
战斗管理器 - 协调所有战斗系统
"""

from typing import List, Dict, Optional, Any
import asyncio
import random

try:
    from .models import Pokemon, Move
    from .battle_engine import battle_engine, battle_ai, BattleState, Action, BattleAction
    from .pokemon_builder import pokemon_builder
    from .data_manager import data_manager
except ImportError:
    from models import Pokemon, Move
    from battle_engine import battle_engine, battle_ai, BattleState, Action, BattleAction
    from pokemon_builder import pokemon_builder
    from data_manager import data_manager


class BattleManager:
    """战斗管理器"""
    
    def __init__(self):
        self.active_battles: Dict[str, BattleState] = {}
    
    def start_battle(
        self,
        player_team: List[Pokemon],
        opponent_team: List[Pokemon],
        battle_id: str = None
    ) -> BattleState:
        """开始新战斗"""
        if not battle_id:
            battle_id = f"battle_{random.randint(1000, 9999)}"
        
        # 验证队伍
        if not self._validate_team(player_team):
            raise ValueError("玩家队伍无效")
        if not self._validate_team(opponent_team):
            raise ValueError("对手队伍无效")
        
        # 开始战斗
        battle_state = battle_engine.start_battle(player_team, opponent_team)
        self.active_battles[battle_id] = battle_state
        
        return battle_state
    
    def process_player_action(
        self,
        battle_id: str,
        action_type: str,
        action_data: Dict[str, Any]
    ) -> List[str]:
        """处理玩家行动"""
        if battle_id not in self.active_battles:
            return ["战斗不存在"]
        
        battle_state = self.active_battles[battle_id]
        if battle_state.is_finished:
            return ["战斗已结束"]
        
        # 创建玩家行动
        player_action = self._create_player_action(battle_state, action_type, action_data)
        if not player_action:
            return ["无效的行动"]
        
        # AI选择行动
        ai_action = battle_ai.choose_action(battle_state)
        
        # 处理回合
        messages = battle_engine.process_turn(player_action, ai_action)
        
        # 检查战斗是否结束
        if battle_state.is_finished:
            del self.active_battles[battle_id]
        
        return messages
    
    def get_battle_state(self, battle_id: str) -> Optional[BattleState]:
        """获取战斗状态"""
        return self.active_battles.get(battle_id)
    
    def end_battle(self, battle_id: str):
        """结束战斗"""
        if battle_id in self.active_battles:
            del self.active_battles[battle_id]
    
    def _validate_team(self, team: List[Pokemon]) -> bool:
        """验证队伍有效性"""
        if not team or len(team) == 0:
            return False
        
        # 检查是否至少有一只宝可梦能战斗
        return any(not pokemon.is_fainted() for pokemon in team)
    
    def _create_player_action(
        self,
        battle_state: BattleState,
        action_type: str,
        action_data: Dict[str, Any]
    ) -> Optional[Action]:
        """创建玩家行动"""
        player_pokemon = battle_state.player_active
        
        if action_type == "move":
            move_index = action_data.get("move_index", 0)
            if 0 <= move_index < len(player_pokemon.moves):
                move = player_pokemon.moves[move_index]
                return Action(
                    type=BattleAction.MOVE,
                    pokemon=player_pokemon,
                    target=battle_state.opponent_active,
                    move=move
                )
        
        elif action_type == "switch":
            switch_index = action_data.get("switch_index", 0)
            available_switches = battle_engine.get_available_switches(True)
            if 0 <= switch_index < len(available_switches):
                switch_to = available_switches[switch_index]
                return Action(
                    type=BattleAction.SWITCH,
                    pokemon=player_pokemon,
                    switch_to=switch_to
                )
        
        elif action_type == "run":
            return Action(
                type=BattleAction.RUN,
                pokemon=player_pokemon
            )
        
        return None
    
    def create_quick_battle(
        self,
        player_preset: str = "starter",
        opponent_preset: str = "starter"
    ) -> str:
        """创建快速战斗"""
        # 创建队伍
        player_team = pokemon_builder.create_preset_team(player_preset)
        opponent_team = pokemon_builder.create_preset_team(opponent_preset)
        
        if not player_team or not opponent_team:
            raise ValueError("无法创建队伍")
        
        # 生成战斗ID
        battle_id = f"quick_{random.randint(10000, 99999)}"
        
        # 开始战斗
        self.start_battle(player_team, opponent_team, battle_id)
        
        return battle_id
    
    def get_battle_summary(self, battle_id: str) -> Dict[str, Any]:
        """获取战斗摘要"""
        battle_state = self.get_battle_state(battle_id)
        if not battle_state:
            return {"error": "战斗不存在"}
        
        return {
            "turn": battle_state.turn_count,
            "player_pokemon": {
                "name": battle_state.player_active.species.name,
                "hp": battle_state.player_active.current_hp,
                "max_hp": battle_state.player_active.calculate_stat('hp'),
                "status": battle_state.player_active.status,
                "moves": [move.name for move in battle_state.player_active.moves if move]
            },
            "opponent_pokemon": {
                "name": battle_state.opponent_active.species.name,
                "hp": battle_state.opponent_active.current_hp,
                "max_hp": battle_state.opponent_active.calculate_stat('hp'),
                "status": battle_state.opponent_active.status
            },
            "conditions": {
                "weather": battle_state.conditions.weather,
                "terrain": battle_state.conditions.terrain
            },
            "is_finished": battle_state.is_finished,
            "winner": battle_state.winner
        }
    
    def get_available_actions(self, battle_id: str) -> Dict[str, Any]:
        """获取可用行动"""
        battle_state = self.get_battle_state(battle_id)
        if not battle_state or battle_state.is_finished:
            return {"error": "战斗不可用"}
        
        player_pokemon = battle_state.player_active
        
        # 可用技能
        moves = []
        for i, move in enumerate(player_pokemon.moves):
            if move:
                moves.append({
                    "index": i,
                    "name": move.name,
                    "type": move.type.value,
                    "power": move.power,
                    "pp": move.pp,
                    "accuracy": move.accuracy
                })
        
        # 可换宝可梦
        switches = []
        available_switches = battle_engine.get_available_switches(True)
        for i, pokemon in enumerate(available_switches):
            switches.append({
                "index": i,
                "name": pokemon.species.name,
                "hp": pokemon.current_hp,
                "max_hp": pokemon.calculate_stat('hp'),
                "status": pokemon.status
            })
        
        return {
            "moves": moves,
            "switches": switches,
            "can_run": True
        }
    
    def simulate_battle(
        self,
        player_team: List[Pokemon],
        opponent_team: List[Pokemon],
        max_turns: int = 100
    ) -> Dict[str, Any]:
        """模拟完整战斗（用于测试）"""
        battle_state = battle_engine.start_battle(player_team, opponent_team)
        battle_log = []
        
        for turn in range(max_turns):
            if battle_state.is_finished:
                break
            
            # AI vs AI
            player_action = battle_ai.choose_action(battle_state)
            opponent_action = battle_ai.choose_action(battle_state)
            
            # 交换行动对象（因为AI总是选择opponent_active）
            player_action.pokemon = battle_state.player_active
            player_action.target = battle_state.opponent_active
            
            turn_messages = battle_engine.process_turn(player_action, opponent_action)
            battle_log.extend(turn_messages)
        
        return {
            "winner": battle_state.winner,
            "turns": battle_state.turn_count,
            "log": battle_log,
            "finished": battle_state.is_finished
        }


# 全局战斗管理器实例
battle_manager = BattleManager()
