"""
数据管理器 - 提供高级数据访问接口
"""

from typing import Dict, List, Optional, Any
try:
    from .data_loader import data_loader
    from .models import Species, Move, Ability, Item, Stats, PokemonType, MoveCategory, Nature
except ImportError:
    from data_loader import data_loader
    from models import Species, Move, Ability, Item, Stats, PokemonType, MoveCategory, Nature


class DataManager:
    """数据管理器"""
    
    def __init__(self):
        self._species_cache: Dict[str, Species] = {}
        self._moves_cache: Dict[str, Move] = {}
        self._abilities_cache: Dict[str, Ability] = {}
        self._items_cache: Dict[str, Item] = {}
        self._natures_cache: Dict[str, Nature] = {}
        
        # 类型相克表
        self._type_chart: Optional[Dict[str, Dict[str, float]]] = None
    
    def get_species(self, species_id: str) -> Optional[Species]:
        """获取宝可梦种族数据"""
        species_id = species_id.lower()
        
        if species_id not in self._species_cache:
            raw_data = data_loader.get_pokemon_data(species_id)
            if not raw_data:
                return None
            
            # 转换为Species对象
            species = self._convert_to_species(species_id, raw_data)
            self._species_cache[species_id] = species
        
        return self._species_cache[species_id]
    
    def get_move(self, move_id: str) -> Optional[Move]:
        """获取技能数据"""
        move_id = move_id.lower()
        
        if move_id not in self._moves_cache:
            raw_data = data_loader.get_move_data(move_id)
            if not raw_data:
                return None
            
            # 转换为Move对象
            move = self._convert_to_move(move_id, raw_data)
            self._moves_cache[move_id] = move
        
        return self._moves_cache[move_id]
    
    def get_ability(self, ability_id: str) -> Optional[Ability]:
        """获取特性数据"""
        ability_id = ability_id.lower()
        
        if ability_id not in self._abilities_cache:
            raw_data = data_loader.get_ability_data(ability_id)
            if not raw_data:
                return None
            
            # 转换为Ability对象
            ability = self._convert_to_ability(ability_id, raw_data)
            self._abilities_cache[ability_id] = ability
        
        return self._abilities_cache[ability_id]
    
    def get_nature(self, nature_name: str) -> Nature:
        """获取性格数据"""
        nature_name = nature_name.lower()
        
        if nature_name not in self._natures_cache:
            # 加载性格数据
            natures_data = data_loader.load_natures()
            nature_data = natures_data.get(nature_name, {})
            
            nature = Nature(
                name=nature_data.get('name', nature_name.title()),
                plus=nature_data.get('plus'),
                minus=nature_data.get('minus')
            )
            self._natures_cache[nature_name] = nature
        
        return self._natures_cache[nature_name]
    
    def get_type_effectiveness(self, attacking_type: str, defending_types: List[str]) -> float:
        """获取属性相克倍率"""
        if self._type_chart is None:
            self._load_type_chart()
        
        effectiveness = 1.0
        attacking_type = attacking_type.lower()
        
        for defending_type in defending_types:
            defending_type = defending_type.lower()
            if attacking_type in self._type_chart:
                type_data = self._type_chart[attacking_type]
                if 'damageTaken' in type_data:
                    damage_taken = type_data['damageTaken']
                    if defending_type in damage_taken:
                        value = damage_taken[defending_type]
                        if value == 1:  # 不很有效
                            effectiveness *= 0.5
                        elif value == 2:  # 非常有效
                            effectiveness *= 2.0
                        elif value == 3:  # 无效
                            effectiveness *= 0.0
        
        return effectiveness
    
    def _load_type_chart(self):
        """加载属性相克表"""
        try:
            self._type_chart = data_loader.load_typechart()
        except Exception:
            # 如果加载失败，使用默认的空表
            self._type_chart = {}
    
    def _convert_to_species(self, species_id: str, raw_data: Dict[str, Any]) -> Species:
        """将原始数据转换为Species对象"""
        # 处理基础数值
        base_stats_data = raw_data.get('baseStats', {})
        base_stats = Stats(
            hp=base_stats_data.get('hp', 0),
            atk=base_stats_data.get('atk', 0),
            def_=base_stats_data.get('def', 0),
            spa=base_stats_data.get('spa', 0),
            spd=base_stats_data.get('spd', 0),
            spe=base_stats_data.get('spe', 0)
        )
        
        # 处理属性
        types = []
        for type_name in raw_data.get('types', []):
            try:
                types.append(PokemonType(type_name))
            except ValueError:
                # 如果属性名不在枚举中，跳过
                continue
        
        return Species(
            id=species_id,
            name=raw_data.get('name', species_id.title()),
            num=raw_data.get('num', 0),
            types=types,
            base_stats=base_stats,
            abilities=raw_data.get('abilities', {}),
            height=raw_data.get('heightm', 0.0),
            weight=raw_data.get('weightkg', 0.0),
            color=raw_data.get('color', ''),
            egg_groups=raw_data.get('eggGroups', []),
            gender_ratio=raw_data.get('genderRatio', {})
        )
    
    def _convert_to_move(self, move_id: str, raw_data: Dict[str, Any]) -> Move:
        """将原始数据转换为Move对象"""
        # 处理属性
        move_type = PokemonType.NORMAL
        try:
            if 'type' in raw_data:
                move_type = PokemonType(raw_data['type'])
        except ValueError:
            pass
        
        # 处理分类
        category = MoveCategory.STATUS
        try:
            if 'category' in raw_data:
                category = MoveCategory(raw_data['category'])
        except ValueError:
            pass
        
        return Move(
            id=move_id,
            name=raw_data.get('name', move_id.title()),
            type=move_type,
            category=category,
            power=raw_data.get('basePower', 0),
            accuracy=raw_data.get('accuracy', 100),
            pp=raw_data.get('pp', 5),
            priority=raw_data.get('priority', 0),
            target=raw_data.get('target', 'normal'),
            flags=raw_data.get('flags', {}),
            secondary=raw_data.get('secondary'),
            drain=raw_data.get('drain'),
            recoil=raw_data.get('recoil'),
            heal=raw_data.get('heal')
        )
    
    def _convert_to_ability(self, ability_id: str, raw_data: Dict[str, Any]) -> Ability:
        """将原始数据转换为Ability对象"""
        return Ability(
            id=ability_id,
            name=raw_data.get('name', ability_id.title()),
            rating=raw_data.get('rating', 0.0),
            flags=raw_data.get('flags', {})
        )
    
    def search_pokemon(self, query: str) -> List[Species]:
        """搜索宝可梦"""
        results = []
        pokedex = data_loader.load_pokedex()
        
        query = query.lower()
        for species_id, raw_data in pokedex.items():
            name = raw_data.get('name', '').lower()
            if query in species_id or query in name:
                species = self.get_species(species_id)
                if species:
                    results.append(species)
        
        return results[:10]  # 限制结果数量
    
    def search_moves(self, query: str) -> List[Move]:
        """搜索技能"""
        results = []
        moves = data_loader.load_moves()
        
        query = query.lower()
        for move_id, raw_data in moves.items():
            name = raw_data.get('name', '').lower()
            if query in move_id or query in name:
                move = self.get_move(move_id)
                if move:
                    results.append(move)
        
        return results[:10]  # 限制结果数量


# 全局数据管理器实例
data_manager = DataManager()
