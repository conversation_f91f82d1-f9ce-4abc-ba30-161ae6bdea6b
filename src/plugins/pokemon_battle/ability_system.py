"""
特性系统 - 处理宝可梦特性效果
"""

from typing import Dict, List, Optional, Any, Callable
from enum import Enum
import random


class AbilityTrigger(Enum):
    """特性触发时机"""
    ON_SWITCH_IN = "switch_in"          # 出场时
    ON_DAMAGE_TAKEN = "damage_taken"    # 受到伤害时
    ON_DAMAGE_DEALT = "damage_dealt"    # 造成伤害时
    ON_MOVE_USE = "move_use"            # 使用技能时
    ON_STATUS_APPLY = "status_apply"    # 施加状态时
    ON_WEATHER_CHANGE = "weather_change" # 天气变化时
    ON_TURN_END = "turn_end"            # 回合结束时
    ON_STAT_CHANGE = "stat_change"      # 能力变化时
    PASSIVE = "passive"                 # 被动效果


class AbilitySystem:
    """特性系统"""
    
    def __init__(self):
        # 特性效果处理函数
        self.ability_handlers: Dict[str, Dict[AbilityTrigger, Callable]] = {
            'overgrow': {
                AbilityTrigger.ON_DAMAGE_DEALT: self._overgrow_damage_boost
            },
            'blaze': {
                AbilityTrigger.ON_DAMAGE_DEALT: self._blaze_damage_boost
            },
            'torrent': {
                AbilityTrigger.ON_DAMAGE_DEALT: self._torrent_damage_boost
            },
            'swarm': {
                AbilityTrigger.ON_DAMAGE_DEALT: self._swarm_damage_boost
            },
            'sturdy': {
                AbilityTrigger.ON_DAMAGE_TAKEN: self._sturdy_survive
            },
            'levitate': {
                AbilityTrigger.ON_DAMAGE_TAKEN: self._levitate_immunity
            },
            'flashfire': {
                AbilityTrigger.ON_DAMAGE_TAKEN: self._flash_fire_immunity
            },
            'waterabsorb': {
                AbilityTrigger.ON_DAMAGE_TAKEN: self._water_absorb
            },
            'voltabsorb': {
                AbilityTrigger.ON_DAMAGE_TAKEN: self._volt_absorb
            },
            'immunity': {
                AbilityTrigger.ON_STATUS_APPLY: self._immunity_poison
            },
            'limber': {
                AbilityTrigger.ON_STATUS_APPLY: self._limber_paralysis
            },
            'waterveil': {
                AbilityTrigger.ON_STATUS_APPLY: self._water_veil_burn
            },
            'insomnia': {
                AbilityTrigger.ON_STATUS_APPLY: self._insomnia_sleep
            },
            'vitalspirit': {
                AbilityTrigger.ON_STATUS_APPLY: self._vital_spirit_sleep
            },
            'speedboost': {
                AbilityTrigger.ON_TURN_END: self._speed_boost
            },
            'intimidate': {
                AbilityTrigger.ON_SWITCH_IN: self._intimidate
            },
            'drizzle': {
                AbilityTrigger.ON_SWITCH_IN: self._drizzle
            },
            'drought': {
                AbilityTrigger.ON_SWITCH_IN: self._drought
            },
            'sandstream': {
                AbilityTrigger.ON_SWITCH_IN: self._sand_stream
            },
            'snowwarning': {
                AbilityTrigger.ON_SWITCH_IN: self._snow_warning
            },
        }
    
    def trigger_ability(
        self,
        pokemon,
        trigger: AbilityTrigger,
        battle_context: Dict[str, Any],
        **kwargs
    ) -> List[str]:
        """触发特性效果"""
        messages = []
        
        if not pokemon.ability:
            return messages
        
        ability_id = pokemon.ability.id.lower()
        
        if ability_id in self.ability_handlers:
            handlers = self.ability_handlers[ability_id]
            if trigger in handlers:
                result = handlers[trigger](pokemon, battle_context, **kwargs)
                if result:
                    if isinstance(result, str):
                        messages.append(result)
                    elif isinstance(result, list):
                        messages.extend(result)
        
        return messages
    
    def check_ability_immunity(
        self,
        pokemon,
        effect_type: str,
        **kwargs
    ) -> bool:
        """检查特性是否提供免疫"""
        if not pokemon.ability:
            return False
        
        ability_id = pokemon.ability.id.lower()
        
        # 状态免疫
        if effect_type == 'poison' and ability_id in ['immunity']:
            return True
        elif effect_type == 'paralysis' and ability_id in ['limber']:
            return True
        elif effect_type == 'burn' and ability_id in ['waterveil']:
            return True
        elif effect_type == 'sleep' and ability_id in ['insomnia', 'vitalspirit']:
            return True
        
        # 属性免疫
        elif effect_type == 'ground' and ability_id in ['levitate']:
            return True
        
        return False
    
    def modify_damage(
        self,
        pokemon,
        damage: int,
        move_type: str,
        is_attacker: bool,
        **kwargs
    ) -> int:
        """特性对伤害的修正"""
        if not pokemon.ability:
            return damage
        
        ability_id = pokemon.ability.id.lower()
        
        if is_attacker:
            # 攻击方特性
            if ability_id in ['overgrow', 'blaze', 'torrent', 'swarm']:
                # 危险状态下的属性加成
                hp_ratio = pokemon.current_hp / pokemon.calculate_stat('hp')
                if hp_ratio <= 1/3:
                    type_map = {
                        'overgrow': 'Grass',
                        'blaze': 'Fire',
                        'torrent': 'Water',
                        'swarm': 'Bug'
                    }
                    if move_type == type_map.get(ability_id):
                        return int(damage * 1.5)
        
        return damage
    
    # 特性效果实现
    def _overgrow_damage_boost(self, pokemon, context, **kwargs):
        """茂盛特性 - 草系技能威力提升"""
        # 在modify_damage中处理
        return None
    
    def _blaze_damage_boost(self, pokemon, context, **kwargs):
        """猛火特性 - 火系技能威力提升"""
        # 在modify_damage中处理
        return None
    
    def _torrent_damage_boost(self, pokemon, context, **kwargs):
        """激流特性 - 水系技能威力提升"""
        # 在modify_damage中处理
        return None
    
    def _swarm_damage_boost(self, pokemon, context, **kwargs):
        """虫之预感特性 - 虫系技能威力提升"""
        # 在modify_damage中处理
        return None
    
    def _sturdy_survive(self, pokemon, context, **kwargs):
        """结实特性 - 满血时不会被一击击倒"""
        damage = kwargs.get('damage', 0)
        if pokemon.current_hp == pokemon.calculate_stat('hp') and damage >= pokemon.current_hp:
            # 修改伤害，使其保留1HP
            kwargs['damage'] = pokemon.current_hp - 1
            return f"{pokemon.species.name}的结实特性发动了！"
        return None
    
    def _levitate_immunity(self, pokemon, context, **kwargs):
        """飘浮特性 - 地面系技能无效"""
        move_type = kwargs.get('move_type', '')
        if move_type == 'Ground':
            kwargs['damage'] = 0
            return f"{pokemon.species.name}的飘浮特性使地面系技能无效！"
        return None
    
    def _flash_fire_immunity(self, pokemon, context, **kwargs):
        """引火特性 - 火系技能无效并提升火系威力"""
        move_type = kwargs.get('move_type', '')
        if move_type == 'Fire':
            kwargs['damage'] = 0
            # 设置引火状态
            pokemon.volatile_status['flash_fire'] = True
            return f"{pokemon.species.name}的引火特性发动了！"
        return None
    
    def _water_absorb(self, pokemon, context, **kwargs):
        """储水特性 - 水系技能无效并回复HP"""
        move_type = kwargs.get('move_type', '')
        if move_type == 'Water':
            kwargs['damage'] = 0
            # 回复HP
            max_hp = pokemon.calculate_stat('hp')
            heal = max_hp // 4
            pokemon.current_hp = min(max_hp, pokemon.current_hp + heal)
            return f"{pokemon.species.name}的储水特性发动了！回复了HP！"
        return None
    
    def _volt_absorb(self, pokemon, context, **kwargs):
        """蓄电特性 - 电系技能无效并回复HP"""
        move_type = kwargs.get('move_type', '')
        if move_type == 'Electric':
            kwargs['damage'] = 0
            # 回复HP
            max_hp = pokemon.calculate_stat('hp')
            heal = max_hp // 4
            pokemon.current_hp = min(max_hp, pokemon.current_hp + heal)
            return f"{pokemon.species.name}的蓄电特性发动了！回复了HP！"
        return None
    
    def _immunity_poison(self, pokemon, context, **kwargs):
        """免疫特性 - 中毒免疫"""
        status_type = kwargs.get('status_type', '')
        if 'poison' in status_type.lower():
            return f"{pokemon.species.name}的免疫特性阻止了中毒！"
        return None
    
    def _limber_paralysis(self, pokemon, context, **kwargs):
        """柔软特性 - 麻痹免疫"""
        status_type = kwargs.get('status_type', '')
        if 'paralysis' in status_type.lower():
            return f"{pokemon.species.name}的柔软特性阻止了麻痹！"
        return None
    
    def _water_veil_burn(self, pokemon, context, **kwargs):
        """水幕特性 - 烧伤免疫"""
        status_type = kwargs.get('status_type', '')
        if 'burn' in status_type.lower():
            return f"{pokemon.species.name}的水幕特性阻止了烧伤！"
        return None
    
    def _insomnia_sleep(self, pokemon, context, **kwargs):
        """不眠特性 - 睡眠免疫"""
        status_type = kwargs.get('status_type', '')
        if 'sleep' in status_type.lower():
            return f"{pokemon.species.name}的不眠特性阻止了睡眠！"
        return None
    
    def _vital_spirit_sleep(self, pokemon, context, **kwargs):
        """干劲特性 - 睡眠免疫"""
        status_type = kwargs.get('status_type', '')
        if 'sleep' in status_type.lower():
            return f"{pokemon.species.name}的干劲特性阻止了睡眠！"
        return None
    
    def _speed_boost(self, pokemon, context, **kwargs):
        """加速特性 - 每回合提升速度"""
        current_speed = pokemon.stat_stages['spe']
        if current_speed < 6:
            pokemon.stat_stages['spe'] = min(6, current_speed + 1)
            return f"{pokemon.species.name}的加速特性提升了速度！"
        return None
    
    def _intimidate(self, pokemon, context, **kwargs):
        """威吓特性 - 出场时降低对手攻击"""
        opponent = context.get('opponent')
        if opponent and opponent.stat_stages['atk'] > -6:
            opponent.stat_stages['atk'] = max(-6, opponent.stat_stages['atk'] - 1)
            return f"{pokemon.species.name}的威吓特性降低了对手的攻击！"
        return None
    
    def _drizzle(self, pokemon, context, **kwargs):
        """降雨特性 - 出场时下雨"""
        battle = context.get('battle')
        if battle:
            battle.conditions.weather = 'raindance'
            return f"{pokemon.species.name}的降雨特性使天气变为雨天！"
        return None
    
    def _drought(self, pokemon, context, **kwargs):
        """日照特性 - 出场时晴天"""
        battle = context.get('battle')
        if battle:
            battle.conditions.weather = 'sunnyday'
            return f"{pokemon.species.name}的日照特性使天气变为晴天！"
        return None
    
    def _sand_stream(self, pokemon, context, **kwargs):
        """扬沙特性 - 出场时沙暴"""
        battle = context.get('battle')
        if battle:
            battle.conditions.weather = 'sandstorm'
            return f"{pokemon.species.name}的扬沙特性使天气变为沙暴！"
        return None
    
    def _snow_warning(self, pokemon, context, **kwargs):
        """降雪特性 - 出场时冰雹"""
        battle = context.get('battle')
        if battle:
            battle.conditions.weather = 'hail'
            return f"{pokemon.species.name}的降雪特性使天气变为冰雹！"
        return None


# 全局特性系统实例
ability_system = AbilitySystem()
