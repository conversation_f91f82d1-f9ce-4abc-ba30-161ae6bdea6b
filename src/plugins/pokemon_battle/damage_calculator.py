"""
伤害计算器 - 实现第九世代宝可梦伤害计算公式
"""

import random
import math
from typing import Optional, List, Dict, Any
try:
    from .models import Pokemon, Move, BattleConditions, MoveCategory
    from .data_manager import data_manager
except ImportError:
    from models import Pokemon, Move, BattleConditions, MoveCategory
    from data_manager import data_manager


class DamageCalculator:
    """伤害计算器"""
    
    def __init__(self):
        self.random_factor_range = (0.85, 1.0)  # 随机因子范围
    
    def calculate_damage(
        self,
        attacker: <PERSON><PERSON><PERSON>,
        defender: <PERSON><PERSON><PERSON>,
        move: Move,
        conditions: Optional[BattleConditions] = None,
        critical_hit: Optional[bool] = None,
        random_factor: Optional[float] = None
    ) -> Dict[str, Any]:
        """
        计算伤害
        
        返回包含以下信息的字典：
        - damage: 实际伤害值
        - is_critical: 是否暴击
        - effectiveness: 属性相克倍率
        - random_factor: 随机因子
        - breakdown: 伤害计算详情
        """
        if conditions is None:
            conditions = BattleConditions()
        
        # 状态技能不造成伤害
        if move.category == MoveCategory.STATUS:
            return {
                'damage': 0,
                'is_critical': False,
                'effectiveness': 1.0,
                'random_factor': 1.0,
                'breakdown': {'type': 'status_move'}
            }
        
        # 计算基础伤害
        base_damage = self._calculate_base_damage(attacker, defender, move, conditions)
        
        # 判断暴击
        if critical_hit is None:
            critical_hit = self._is_critical_hit(attacker, move)
        
        # 应用暴击倍率
        if critical_hit:
            base_damage = int(base_damage * 1.5)
        
        # 计算属性相克
        defender_types = [t.value for t in defender.species.types]
        effectiveness = data_manager.get_type_effectiveness(move.type.value, defender_types)
        
        # 应用属性相克
        base_damage = int(base_damage * effectiveness)
        
        # 应用STAB（本系技能加成）
        if move.type in attacker.species.types:
            base_damage = int(base_damage * 1.5)
        
        # 应用随机因子
        if random_factor is None:
            random_factor = random.uniform(*self.random_factor_range)
        
        final_damage = int(base_damage * random_factor)
        
        # 确保至少造成1点伤害（如果不是无效攻击）
        if final_damage == 0 and effectiveness > 0:
            final_damage = 1
        
        return {
            'damage': final_damage,
            'is_critical': critical_hit,
            'effectiveness': effectiveness,
            'random_factor': random_factor,
            'breakdown': {
                'base_damage': base_damage,
                'critical_hit': critical_hit,
                'stab': move.type in attacker.species.types,
                'type_effectiveness': effectiveness
            }
        }
    
    def _calculate_base_damage(
        self,
        attacker: Pokemon,
        defender: Pokemon,
        move: Move,
        conditions: BattleConditions
    ) -> float:
        """计算基础伤害"""
        # 获取攻击力和防御力
        if move.category == MoveCategory.PHYSICAL:
            attack_stat = attacker.get_effective_stat('atk')
            defense_stat = defender.get_effective_stat('def')
        else:  # Special
            attack_stat = attacker.get_effective_stat('spa')
            defense_stat = defender.get_effective_stat('spd')
        
        # 基础伤害公式
        # Damage = ((((2 * Level / 5 + 2) * Power * A / D) / 50) + 2) * Modifiers
        level = attacker.level
        power = move.power
        
        if power == 0:
            return 0
        
        # 第一部分：((2 * Level / 5 + 2) * Power * A / D) / 50
        damage = ((2 * level / 5 + 2) * power * attack_stat / defense_stat) / 50
        
        # 第二部分：+ 2
        damage = damage + 2
        
        return damage
    
    def _is_critical_hit(self, attacker: Pokemon, move: Move) -> bool:
        """判断是否暴击"""
        # 基础暴击率
        crit_ratio = 1
        
        # 技能的暴击率修正
        if hasattr(move, 'critRatio'):
            crit_ratio = getattr(move, 'critRatio', 1)
        
        # 暴击率阶段
        crit_stage = 0
        
        # 根据暴击阶段计算暴击概率
        crit_chances = {
            0: 1/24,    # 4.17%
            1: 1/8,     # 12.5%
            2: 1/2,     # 50%
            3: 1,       # 100%
        }
        
        effective_stage = min(3, max(0, crit_stage + crit_ratio - 1))
        crit_chance = crit_chances[effective_stage]
        
        return random.random() < crit_chance
    
    def calculate_healing(
        self,
        pokemon: Pokemon,
        move: Move,
        damage_dealt: int = 0
    ) -> int:
        """计算治疗量"""
        if not move.heal and not move.drain:
            return 0
        
        max_hp = pokemon.calculate_stat('hp')
        
        if move.heal:
            # 固定治疗量
            heal_fraction = move.heal[0] / move.heal[1]
            return int(max_hp * heal_fraction)
        
        elif move.drain:
            # 吸收伤害
            drain_fraction = move.drain[0] / move.drain[1]
            return int(damage_dealt * drain_fraction)
        
        return 0
    
    def calculate_recoil(self, pokemon: Pokemon, move: Move, damage_dealt: int) -> int:
        """计算反作用力伤害"""
        if not move.recoil:
            return 0
        
        recoil_fraction = move.recoil[0] / move.recoil[1]
        return int(damage_dealt * recoil_fraction)
    
    def apply_weather_effects(
        self,
        damage: int,
        move: Move,
        weather: Optional[str]
    ) -> int:
        """应用天气效果"""
        if not weather:
            return damage
        
        # 晴天效果
        if weather == 'sunnyday':
            if move.type.value == 'Fire':
                return int(damage * 1.5)
            elif move.type.value == 'Water':
                return int(damage * 0.5)
        
        # 雨天效果
        elif weather == 'raindance':
            if move.type.value == 'Water':
                return int(damage * 1.5)
            elif move.type.value == 'Fire':
                return int(damage * 0.5)
        
        # 沙暴效果
        elif weather == 'sandstorm':
            if move.type.value == 'Rock':
                return int(damage * 1.5)
        
        # 冰雹效果
        elif weather == 'hail':
            if move.type.value == 'Ice':
                return int(damage * 1.5)
        
        return damage
    
    def get_effectiveness_message(self, effectiveness: float) -> str:
        """获取属性相克效果消息"""
        if effectiveness > 1:
            return "效果拔群！"
        elif effectiveness < 1 and effectiveness > 0:
            return "效果不理想..."
        elif effectiveness == 0:
            return "对手没有受到影响！"
        else:
            return ""


# 全局伤害计算器实例
damage_calculator = DamageCalculator()
