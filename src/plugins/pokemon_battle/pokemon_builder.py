"""
宝可梦构建器 - 创建和配置宝可梦实例
"""

import random
from typing import List, Optional, Dict, Any
try:
    from .models import Pokemon, Species, Move, Ability, Item, Stats, Nature, Gender
    from .data_manager import data_manager
except ImportError:
    # 处理直接运行时的导入
    from models import Pokemon, Species, Move, Ability, Item, Stats, Nature, Gender
    from data_manager import data_manager


class PokemonBuilder:
    """宝可梦构建器"""
    
    def __init__(self):
        self.default_level = 50
    
    def create_pokemon(
        self,
        species_id: str,
        level: int = None,
        moves: List[str] = None,
        ability: str = None,
        item: str = None,
        nature: str = None,
        ivs: Dict[str, int] = None,
        evs: Dict[str, int] = None,
        gender: str = None
    ) -> Optional[Pokemon]:
        """创建宝可梦实例"""
        
        # 获取种族数据
        species = data_manager.get_species(species_id)
        if not species:
            return None
        
        # 设置等级
        if level is None:
            level = self.default_level
        
        # 设置性别
        pokemon_gender = self._determine_gender(species, gender)
        
        # 设置性格
        pokemon_nature = self._get_nature(nature)
        
        # 设置特性
        pokemon_ability = self._get_ability(species, ability)
        
        # 设置道具
        pokemon_item = self._get_item(item)
        
        # 设置个体值
        pokemon_ivs = self._get_ivs(ivs)
        
        # 设置努力值
        pokemon_evs = self._get_evs(evs)
        
        # 设置技能
        pokemon_moves = self._get_moves(species, moves)
        
        # 创建宝可梦实例
        pokemon = Pokemon(
            species=species,
            level=level,
            gender=pokemon_gender,
            nature=pokemon_nature,
            ability=pokemon_ability,
            item=pokemon_item,
            moves=pokemon_moves,
            ivs=pokemon_ivs,
            evs=pokemon_evs
        )
        
        return pokemon
    
    def create_random_pokemon(self, species_id: str, level: int = None) -> Optional[Pokemon]:
        """创建随机配置的宝可梦"""
        species = data_manager.get_species(species_id)
        if not species:
            return None
        
        # 随机选择技能
        available_moves = self.get_learnable_moves(species_id)
        random_moves = random.sample(available_moves, min(4, len(available_moves)))
        
        # 随机选择特性
        abilities = list(species.abilities.values())
        random_ability = random.choice(abilities) if abilities else None
        
        # 随机性格
        natures = ['hardy', 'lonely', 'brave', 'adamant', 'naughty', 'bold', 'docile', 
                  'relaxed', 'impish', 'lax', 'timid', 'hasty', 'serious', 'jolly', 
                  'naive', 'modest', 'mild', 'quiet', 'bashful', 'rash', 'calm', 
                  'gentle', 'sassy', 'careful', 'quirky']
        random_nature = random.choice(natures)
        
        return self.create_pokemon(
            species_id=species_id,
            level=level,
            moves=random_moves,
            ability=random_ability,
            nature=random_nature
        )
    
    def get_learnable_moves(self, species_id: str) -> List[str]:
        """获取宝可梦可学习的技能列表"""
        # 这里应该从learnsets数据中获取，暂时返回一些基础技能
        basic_moves = [
            'tackle', 'scratch', 'growl', 'leer', 'thundershock', 'ember', 
            'watergun', 'vinewhip', 'gust', 'peck', 'bite', 'pound'
        ]
        
        # 根据宝可梦属性添加相应的技能
        species = data_manager.get_species(species_id)
        if species:
            type_moves = {
                'Fire': ['ember', 'flamethrower', 'fireblast'],
                'Water': ['watergun', 'surf', 'hydropump'],
                'Grass': ['vinewhip', 'razorleaf', 'solarbeam'],
                'Electric': ['thundershock', 'thunderbolt', 'thunder'],
                'Normal': ['tackle', 'bodyslam', 'hyperbeam'],
                'Flying': ['gust', 'wingattack', 'hurricane'],
                'Poison': ['poisonsting', 'sludgebomb', 'toxic'],
                'Ground': ['earthquake', 'dig', 'mudslap'],
                'Rock': ['rockthrow', 'rockslide', 'stoneedge'],
                'Bug': ['stringshot', 'bugbite', 'megahorn'],
                'Ghost': ['lick', 'shadowball', 'shadowclaw'],
                'Steel': ['metalclaw', 'ironhead', 'meteormash'],
                'Fighting': ['karatechop', 'machpunch', 'closecombat'],
                'Psychic': ['confusion', 'psychic', 'futuresight'],
                'Ice': ['icepunch', 'icebeam', 'blizzard'],
                'Dragon': ['dragonrage', 'dragonpulse', 'outrage'],
                'Dark': ['bite', 'crunch', 'darkpulse'],
                'Fairy': ['fairywind', 'moonblast', 'playrough']
            }
            
            for pokemon_type in species.types:
                type_name = pokemon_type.value
                if type_name in type_moves:
                    basic_moves.extend(type_moves[type_name])
        
        # 去重并返回
        return list(set(basic_moves))
    
    def _determine_gender(self, species: Species, gender: str = None) -> Gender:
        """确定宝可梦性别"""
        if gender:
            try:
                return Gender(gender.upper())
            except ValueError:
                pass
        
        # 根据性别比例随机确定
        gender_ratio = species.gender_ratio
        if not gender_ratio:
            return Gender.GENDERLESS
        
        male_ratio = gender_ratio.get('M', 0)
        if random.random() < male_ratio:
            return Gender.MALE
        else:
            return Gender.FEMALE
    
    def _get_nature(self, nature: str = None) -> Nature:
        """获取性格"""
        if nature:
            return data_manager.get_nature(nature)
        else:
            # 默认勤奋性格
            return data_manager.get_nature('hardy')
    
    def _get_ability(self, species: Species, ability: str = None) -> Optional[Ability]:
        """获取特性"""
        if ability:
            return data_manager.get_ability(ability)
        
        # 从可用特性中随机选择
        abilities = list(species.abilities.values())
        if abilities:
            ability_id = random.choice(abilities)
            return data_manager.get_ability(ability_id)
        
        return None
    
    def _get_item(self, item: str = None) -> Optional[Item]:
        """获取道具"""
        if item:
            # TODO: 实现道具系统
            pass
        return None
    
    def _get_ivs(self, ivs: Dict[str, int] = None) -> Stats:
        """获取个体值"""
        if ivs:
            return Stats(
                hp=ivs.get('hp', 31),
                atk=ivs.get('atk', 31),
                def_=ivs.get('def', 31),
                spa=ivs.get('spa', 31),
                spd=ivs.get('spd', 31),
                spe=ivs.get('spe', 31)
            )
        else:
            # 默认满个体值
            return Stats(31, 31, 31, 31, 31, 31)
    
    def _get_evs(self, evs: Dict[str, int] = None) -> Stats:
        """获取努力值"""
        if evs:
            return Stats(
                hp=min(252, evs.get('hp', 0)),
                atk=min(252, evs.get('atk', 0)),
                def_=min(252, evs.get('def', 0)),
                spa=min(252, evs.get('spa', 0)),
                spd=min(252, evs.get('spd', 0)),
                spe=min(252, evs.get('spe', 0))
            )
        else:
            # 默认无努力值
            return Stats(0, 0, 0, 0, 0, 0)
    
    def _get_moves(self, species: Species, moves: List[str] = None) -> List[Move]:
        """获取技能列表"""
        pokemon_moves = []
        
        if moves:
            for move_id in moves:
                move = data_manager.get_move(move_id)
                if move:
                    pokemon_moves.append(move)
        
        # 如果技能不足4个，补充基础技能
        if len(pokemon_moves) < 4:
            learnable_moves = self.get_learnable_moves(species.id)
            for move_id in learnable_moves:
                if len(pokemon_moves) >= 4:
                    break
                move = data_manager.get_move(move_id)
                if move and move not in pokemon_moves:
                    pokemon_moves.append(move)
        
        return pokemon_moves[:4]  # 最多4个技能
    
    def create_preset_team(self, preset_name: str) -> List[Pokemon]:
        """创建预设队伍"""
        presets = {
            'starter': [
                {'species_id': 'bulbasaur', 'moves': ['tackle', 'vinewhip', 'growl', 'leechseed']},
                {'species_id': 'charmander', 'moves': ['scratch', 'ember', 'growl', 'smokescreen']},
                {'species_id': 'squirtle', 'moves': ['tackle', 'watergun', 'withdraw', 'bubble']},
            ],
            'legendary': [
                {'species_id': 'mewtwo', 'moves': ['psychic', 'shadowball', 'recover', 'calmmind']},
                {'species_id': 'mew', 'moves': ['psychic', 'softboiled', 'transform', 'metronome']},
            ],
            'competitive': [
                {'species_id': 'garchomp', 'moves': ['earthquake', 'dragonrush', 'stoneedge', 'swordsdance']},
                {'species_id': 'metagross', 'moves': ['meteormash', 'earthquake', 'explosion', 'agility']},
                {'species_id': 'salamence', 'moves': ['dragonpulse', 'flamethrower', 'earthquake', 'dragondance']},
            ]
        }
        
        team = []
        if preset_name in presets:
            for pokemon_data in presets[preset_name]:
                pokemon = self.create_pokemon(**pokemon_data)
                if pokemon:
                    team.append(pokemon)
        
        return team


# 全局宝可梦构建器实例
pokemon_builder = PokemonBuilder()
