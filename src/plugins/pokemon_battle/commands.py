"""
NoneBot命令处理器
"""

from typing import Dict, List, Optional, Any
import asyncio
from nonebot import on_command, on_message
from nonebot.adapters.onebot.v11 import Bot, Event, MessageEvent, GroupMessageEvent, PrivateMessageEvent
from nonebot.params import CommandArg, ArgStr
from nonebot.matcher import Matcher
from nonebot.message import run_preprocessor
from nonebot.typing import T_State

from .battle_manager import battle_manager
from .pokemon_builder import pokemon_builder
from .data_manager import data_manager


# 全局战斗会话存储
battle_sessions: Dict[str, Any] = {}


def get_session_id(event: MessageEvent) -> str:
    """获取会话ID"""
    if isinstance(event, GroupMessageEvent):
        return f"group_{event.group_id}_{event.user_id}"
    else:
        return f"private_{event.user_id}"


# 宝可梦战斗命令
pokemon_battle = on_command("pokemon", aliases={"宝可梦", "pm"}, priority=5)

@pokemon_battle.handle()
async def handle_pokemon_command(bot: <PERSON><PERSON>, event: MessageEvent, matcher: Matcher, args=CommandArg()):
    """处理宝可梦命令"""
    session_id = get_session_id(event)
    args_str = str(args).strip()
    
    if not args_str:
        await matcher.send("""
🎮 宝可梦战斗系统

可用命令：
/pokemon battle - 开始战斗
/pokemon team - 管理队伍
/pokemon info <宝可梦名> - 查看宝可梦信息
/pokemon move <技能名> - 查看技能信息
/pokemon status - 查看当前状态
        """)
        return
    
    command_parts = args_str.split()
    subcommand = command_parts[0].lower()
    
    if subcommand == "battle":
        await start_battle(bot, event, matcher, command_parts[1:])
    elif subcommand == "team":
        await manage_team(bot, event, matcher, command_parts[1:])
    elif subcommand == "info":
        await show_pokemon_info(bot, event, matcher, command_parts[1:])
    elif subcommand == "move":
        await show_move_info(bot, event, matcher, command_parts[1:])
    elif subcommand == "status":
        await show_battle_status(bot, event, matcher)
    else:
        await matcher.send("未知命令，请使用 /pokemon 查看帮助")


async def start_battle(bot: Bot, event: MessageEvent, matcher: Matcher, args: List[str]):
    """开始战斗"""
    session_id = get_session_id(event)
    
    # 检查是否已有进行中的战斗
    if session_id in battle_sessions:
        await matcher.send("你已经在战斗中了！使用 /pokemon status 查看当前状态")
        return
    
    # 创建玩家队伍（使用预设）
    preset = args[0] if args else "starter"
    player_team = pokemon_builder.create_preset_team(preset)
    
    if not player_team:
        await matcher.send("创建队伍失败，请检查预设名称")
        return
    
    # 创建对手队伍
    opponent_team = pokemon_builder.create_preset_team("starter")
    
    # 开始战斗
    try:
        battle_state = battle_manager.start_battle(player_team, opponent_team)
        battle_sessions[session_id] = {
            'battle_state': battle_state,
            'waiting_for_action': True
        }
        
        message = f"""
⚔️ 战斗开始！

你的宝可梦：{battle_state.player_active.species.name} (HP: {battle_state.player_active.current_hp}/{battle_state.player_active.calculate_stat('hp')})
对手宝可梦：{battle_state.opponent_active.species.name} (HP: {battle_state.opponent_active.current_hp}/{battle_state.opponent_active.calculate_stat('hp')})

请选择行动：
1. 攻击 - 使用技能
2. 换宝可梦
3. 逃跑

回复数字选择行动类型
        """
        await matcher.send(message)
        
    except Exception as e:
        await matcher.send(f"战斗启动失败：{str(e)}")


async def manage_team(bot: Bot, event: MessageEvent, matcher: Matcher, args: List[str]):
    """管理队伍"""
    if not args:
        await matcher.send("""
👥 队伍管理

可用预设：
- starter: 御三家队伍
- legendary: 传说宝可梦队伍
- competitive: 竞技队伍

使用方法：/pokemon team <预设名>
        """)
        return
    
    preset = args[0]
    team = pokemon_builder.create_preset_team(preset)
    
    if not team:
        await matcher.send("未找到该预设队伍")
        return
    
    team_info = f"📋 {preset} 队伍：\n"
    for i, pokemon in enumerate(team, 1):
        team_info += f"{i}. {pokemon.species.name} (Lv.{pokemon.level})\n"
        team_info += f"   HP: {pokemon.calculate_stat('hp')} | 攻击: {pokemon.calculate_stat('atk')} | 防御: {pokemon.calculate_stat('def')}\n"
        team_info += f"   特攻: {pokemon.calculate_stat('spa')} | 特防: {pokemon.calculate_stat('spd')} | 速度: {pokemon.calculate_stat('spe')}\n"
        if pokemon.moves:
            moves = ", ".join([move.name for move in pokemon.moves[:4]])
            team_info += f"   技能: {moves}\n"
        team_info += "\n"
    
    await matcher.send(team_info)


async def show_pokemon_info(bot: Bot, event: MessageEvent, matcher: Matcher, args: List[str]):
    """显示宝可梦信息"""
    if not args:
        await matcher.send("请指定宝可梦名称，例如：/pokemon info bulbasaur")
        return
    
    pokemon_name = args[0].lower()
    species = data_manager.get_species(pokemon_name)
    
    if not species:
        await matcher.send(f"未找到宝可梦：{pokemon_name}")
        return
    
    types_str = "/".join([t.value for t in species.types])
    abilities_str = ", ".join(species.abilities.values())
    
    info = f"""
📖 {species.name} (#{species.num})

属性：{types_str}
身高：{species.height}m
体重：{species.weight}kg

种族值：
HP: {species.base_stats.hp}
攻击: {species.base_stats.atk}
防御: {species.base_stats.def_}
特攻: {species.base_stats.spa}
特防: {species.base_stats.spd}
速度: {species.base_stats.spe}

特性：{abilities_str}
    """
    
    await matcher.send(info)


async def show_move_info(bot: Bot, event: MessageEvent, matcher: Matcher, args: List[str]):
    """显示技能信息"""
    if not args:
        await matcher.send("请指定技能名称，例如：/pokemon move tackle")
        return
    
    move_name = args[0].lower()
    move = data_manager.get_move(move_name)
    
    if not move:
        await matcher.send(f"未找到技能：{move_name}")
        return
    
    accuracy_str = str(move.accuracy) if isinstance(move.accuracy, int) else "必中"
    
    info = f"""
⚡ {move.name}

属性：{move.type.value}
分类：{move.category.value}
威力：{move.power if move.power > 0 else "-"}
命中率：{accuracy_str}
PP：{move.pp}
优先度：{move.priority}
    """
    
    await matcher.send(info)


async def show_battle_status(bot: Bot, event: MessageEvent, matcher: Matcher):
    """显示战斗状态"""
    session_id = get_session_id(event)
    
    if session_id not in battle_sessions:
        await matcher.send("当前没有进行中的战斗")
        return
    
    battle_state = battle_sessions[session_id]['battle_state']
    
    if battle_state.is_finished:
        winner = "你" if battle_state.winner == "player" else "对手"
        await matcher.send(f"战斗已结束，{winner}获胜！")
        del battle_sessions[session_id]
        return
    
    player_pokemon = battle_state.player_active
    opponent_pokemon = battle_state.opponent_active
    
    status = f"""
⚔️ 战斗状态 (回合 {battle_state.turn_count})

你的宝可梦：{player_pokemon.species.name}
HP: {player_pokemon.current_hp}/{player_pokemon.calculate_stat('hp')} ({player_pokemon.get_hp_percentage():.1%})
状态：{player_pokemon.status or "正常"}

对手宝可梦：{opponent_pokemon.species.name}
HP: {opponent_pokemon.current_hp}/{opponent_pokemon.calculate_stat('hp')} ({opponent_pokemon.get_hp_percentage():.1%})
状态：{opponent_pokemon.status or "正常"}

天气：{battle_state.conditions.weather or "无"}
    """
    
    await matcher.send(status)


# 战斗行动处理
battle_action = on_message(priority=10, block=False)

@battle_action.handle()
async def handle_battle_action(bot: Bot, event: MessageEvent, matcher: Matcher):
    """处理战斗中的行动"""
    session_id = get_session_id(event)
    
    if session_id not in battle_sessions:
        return
    
    session = battle_sessions[session_id]
    if not session.get('waiting_for_action'):
        return
    
    message = str(event.get_message()).strip()
    
    try:
        action_type = int(message)
        if action_type == 1:
            await handle_attack_choice(bot, event, matcher, session)
        elif action_type == 2:
            await handle_switch_choice(bot, event, matcher, session)
        elif action_type == 3:
            await handle_run_choice(bot, event, matcher, session)
        else:
            await matcher.send("请选择有效的行动 (1-3)")
    except ValueError:
        # 不是数字，可能是其他命令，不处理
        return


async def handle_attack_choice(bot: Bot, event: MessageEvent, matcher: Matcher, session: Dict[str, Any]):
    """处理攻击选择"""
    battle_state = session['battle_state']
    moves = battle_state.player_active.moves
    
    if not moves:
        await matcher.send("没有可用的技能！")
        return
    
    move_list = "选择技能：\n"
    for i, move in enumerate(moves, 1):
        move_list += f"{i}. {move.name} (威力: {move.power}, PP: {move.pp})\n"
    
    await matcher.send(move_list + "\n回复技能编号")
    session['action_type'] = 'attack'
    session['waiting_for_action'] = True


async def handle_switch_choice(bot: Bot, event: MessageEvent, matcher: Matcher, session: Dict[str, Any]):
    """处理换宝可梦选择"""
    battle_state = session['battle_state']
    available_pokemon = [p for p in battle_state.player_team 
                        if p != battle_state.player_active and not p.is_fainted()]
    
    if not available_pokemon:
        await matcher.send("没有可换的宝可梦！")
        return
    
    pokemon_list = "选择宝可梦：\n"
    for i, pokemon in enumerate(available_pokemon, 1):
        hp_percent = pokemon.get_hp_percentage()
        pokemon_list += f"{i}. {pokemon.species.name} (HP: {hp_percent:.1%})\n"
    
    await matcher.send(pokemon_list + "\n回复宝可梦编号")
    session['action_type'] = 'switch'
    session['available_switches'] = available_pokemon
    session['waiting_for_action'] = True


async def handle_run_choice(bot: Bot, event: MessageEvent, matcher: Matcher, session: Dict[str, Any]):
    """处理逃跑选择"""
    session_id = get_session_id(event)
    await matcher.send("逃跑成功！战斗结束。")
    del battle_sessions[session_id]
