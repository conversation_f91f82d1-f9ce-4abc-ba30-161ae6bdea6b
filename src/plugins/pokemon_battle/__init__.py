"""
宝可梦战斗插件

一个完整的第九世代宝可梦战斗系统，基于Pokemon Showdown数据实现。
支持完整的单打对战，包括所有官方算法和逻辑。
"""

from nonebot import require
from nonebot.plugin import PluginMetadata

# 暂时注释掉，避免依赖问题
# require("nonebot_plugin_alconna")

# from . import commands

__plugin_meta__ = PluginMetadata(
    name="宝可梦战斗",
    description="完整的第九世代宝可梦战斗系统",
    usage="""
    /pokemon battle - 开始一场宝可梦对战
    /pokemon team - 管理你的队伍
    /pokemon info <宝可梦名称> - 查看宝可梦信息
    /pokemon move <技能名称> - 查看技能信息
    """,
    type="application",
    homepage="https://github.com/your-repo/pokemon-battle",
    supported_adapters={"~onebot.v11"},
)

__all__ = ["__plugin_meta__"]
